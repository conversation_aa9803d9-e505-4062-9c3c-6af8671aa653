<template>
  <div class="stripe-payment-form">
    <div class="payment-header">
      <h3>信用卡支付</h3>
      <p>订单号：{{ orderNumber }} | 金额：${{ amount }}</p>
    </div>

    <div class="payment-form" v-loading="loading">
      <!-- 持卡人姓名 -->
      <div class="form-group">
        <label>持卡人姓名</label>
        <input v-model="cardholderName" type="text" placeholder="请输入持卡人姓名" class="form-input"
          :class="{ error: errors.cardholderName }" />
        <div v-if="errors.cardholderName" class="error-text">{{ errors.cardholderName }}</div>
      </div>

      <!-- 信用卡号 -->
      <div class="form-group">
        <label>信用卡号</label>
        <div id="card-number" class="stripe-input" :class="{ error: errors.cardNumber }"></div>
        <div v-if="errors.cardNumber" class="error-text">{{ errors.cardNumber }}</div>
      </div>

      <!-- 有效期和CVV -->
      <div class="form-row">
        <div class="form-group">
          <label>有效期</label>
          <div id="card-expiry" class="stripe-input" :class="{ error: errors.cardExpiry }"></div>
          <div v-if="errors.cardExpiry" class="error-text">{{ errors.cardExpiry }}</div>
        </div>
        <div class="form-group">
          <label>CVV</label>
          <div id="card-cvc" class="stripe-input" :class="{ error: errors.cardCvc }"></div>
          <div v-if="errors.cardCvc" class="error-text">{{ errors.cardCvc }}</div>
        </div>
      </div>

      <!-- 支付按钮 -->
      <button @click="handlePayment" :disabled="processing" class="pay-button">
        {{ processing ? '处理中...' : `支付 $${amount}` }}
      </button>

      <!-- 测试提示 -->
      <div class="test-info">
        <p><strong>测试卡号：</strong>4242 4242 4242 4242</p>
        <p><strong>有效期：</strong>任何未来日期 | <strong>CVV：</strong>任何3位数字</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { CaptureStripePayment } from '@/api/payment'

// Props
const props = defineProps({
  publishableKey: {
    type: String,
    required: true
  },
  amount: {
    type: [Number, String],
    required: true
  },
  orderNumber: {
    type: String,
    required: true
  },
  currency: {
    type: String,
    default: 'usd'
  }
})

// Emits
const emit = defineEmits(['success', 'error'])

// 响应式数据
const loading = ref(false)
const processing = ref(false)
const cardholderName = ref('')

// 错误信息
const errors = reactive({
  cardholderName: '',
  cardNumber: '',
  cardExpiry: '',
  cardCvc: ''
})

// Stripe 相关变量
let stripe = null
let elements = null
let cardNumber = null
let cardExpiry = null
let cardCvc = null

// 初始化 Stripe
const initStripe = async () => {
  try {
    loading.value = true

    // 加载 Stripe SDK
    if (!window.Stripe) {
      const script = document.createElement('script')
      script.src = 'https://js.stripe.com/v3/'
      document.head.appendChild(script)

      await new Promise((resolve, reject) => {
        script.onload = resolve
        script.onerror = reject
      })
    }

    // 初始化 Stripe
    stripe = window.Stripe(props.publishableKey)
    elements = stripe.elements()

    // 创建卡片元素
    const style = {
      base: {
        fontSize: '16px',
        color: '#424770',
        '::placeholder': {
          color: '#aab7c4',
        },
      },
    }

    cardNumber = elements.create('cardNumber', { style })
    cardExpiry = elements.create('cardExpiry', { style })
    cardCvc = elements.create('cardCvc', { style })

    // 挂载元素
    cardNumber.mount('#card-number')
    cardExpiry.mount('#card-expiry')
    cardCvc.mount('#card-cvc')

    // 监听错误
    cardNumber.on('change', (event) => {
      errors.cardNumber = event.error ? event.error.message : ''
    })
    cardExpiry.on('change', (event) => {
      errors.cardExpiry = event.error ? event.error.message : ''
    })
    cardCvc.on('change', (event) => {
      errors.cardCvc = event.error ? event.error.message : ''
    })

  } catch (error) {
    console.error('Stripe 初始化失败:', error)
    ElMessage.error('支付系统初始化失败')
  } finally {
    loading.value = false
  }
}

// 处理支付
const handlePayment = async () => {
  try {
    // 验证持卡人姓名
    if (!cardholderName.value.trim()) {
      errors.cardholderName = '请输入持卡人姓名'
      return
    }
    errors.cardholderName = ''

    processing.value = true

    // 创建 Token
    const { token, error } = await stripe.createToken(cardNumber, {
      name: cardholderName.value,
    })

    if (error) {
      throw new Error(error.message)
    }

    // 调用后端接口处理支付
    const result = await CaptureStripePayment({
      token: token.id,
      order_number: props.orderNumber,
      amount: props.amount,
      currency: props.currency
    })

    if (result.status === 'success') {
      ElMessage.success('支付成功！')
      emit('success', {
        token: token.id,
        orderNumber: props.orderNumber,
        result
      })
    } else {
      throw new Error(result.message || '支付失败')
    }

  } catch (error) {
    console.error('支付处理失败:', error)
    ElMessage.error(error.message || '支付处理失败，请重试')
    emit('error', error)
  } finally {
    processing.value = false
  }
}

// 组件挂载时初始化
onMounted(() => {
  initStripe()
})

// 组件卸载时清理
onUnmounted(() => {
  if (cardNumber) cardNumber.destroy()
  if (cardExpiry) cardExpiry.destroy()
  if (cardCvc) cardCvc.destroy()
})
</script>

<style lang="scss" scoped>
.stripe-payment-form {
  max-width: 500px;
  margin: 0 auto;
  padding: 2rem;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

  .payment-header {
    text-align: center;
    margin-bottom: 2rem;

    h3 {
      margin: 0 0 0.5rem 0;
      color: #333;
      font-size: 1.5rem;
    }

    p {
      margin: 0;
      color: #666;
      font-size: 0.9rem;
    }
  }

  .payment-form {
    .form-group {
      margin-bottom: 1.5rem;

      label {
        display: block;
        margin-bottom: 0.5rem;
        color: #333;
        font-weight: 500;
        font-size: 0.9rem;
      }

      .form-input {
        width: 100%;
        padding: 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 1rem;

        &:focus {
          outline: none;
          border-color: #409eff;
        }

        &.error {
          border-color: #f56c6c;
        }
      }

      .stripe-input {
        padding: 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background: #fff;

        &:focus-within {
          border-color: #409eff;
        }

        &.error {
          border-color: #f56c6c;
        }
      }

      .error-text {
        color: #f56c6c;
        font-size: 0.8rem;
        margin-top: 0.25rem;
      }
    }

    .form-row {
      display: flex;
      gap: 1rem;

      .form-group {
        flex: 1;
      }
    }

    .pay-button {
      width: 100%;
      padding: 15px;
      background: #409eff;
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      margin-top: 1rem;

      &:hover:not(:disabled) {
        background: #337ecc;
      }

      &:disabled {
        background: #ccc;
        cursor: not-allowed;
      }
    }

    .test-info {
      margin-top: 1.5rem;
      padding: 1rem;
      background: #f8f9fa;
      border-radius: 4px;
      border-left: 4px solid #409eff;

      p {
        margin: 0.25rem 0;
        font-size: 0.8rem;
        color: #666;
      }
    }
  }
}
</style>
