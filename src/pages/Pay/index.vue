<template>
    <div class="pay_box">
        <div class="pay_box_main">
            <!-- 订单信息 -->
            <div class="order-info">
                <h2 class="order-title">{{ $t('payment.orderSubmitted', '订单提交成功，请付款') }}</h2>
                <div class="order-details">
                    <div class="order-item" v-if="orderInfo.orderNumber">
                        <span class="label">{{ $t('payment.orderNumber', '订单号') }}：</span>
                        <span class="value">{{ orderInfo.orderNumber }}</span>
                    </div>
                    <div class="order-item">
                        <span class="label">{{ $t('payment.amount', '支付金额') }}：</span>
                        <span class="value amount">{{ formatAmount(orderInfo.amount || 100) }}</span>
                    </div>
                </div>
            </div>

            <!-- 支付方式选择 -->
            <div class="payment-methods" v-loading="loading">
                <h3 class="section-title">{{ $t('payment.selectMethod', '选择支付方式') }}</h3>
                <div v-if="availablePaymentMethods.length === 0 && !loading" class="no-payment-methods">
                    <p>暂无可用的支付方式</p>
                </div>
                <el-radio-group v-else v-model="selectedPaymentMethod" class="payment-options">
                    <el-radio v-for="method in availablePaymentMethods" :key="method.id" :value="method.id"
                        class="payment-option">
                        <div class="payment-option-content">
                            <img :src="method.icon" :alt="method.name" class="payment-logo" />
                            <span>{{ method.name }}</span>
                        </div>
                    </el-radio>
                </el-radio-group>
            </div>

            <!-- Stripe 支付表单 -->
            <div v-if="selectedPaymentMethod === 'stripe'" class="payment-form-container">
                <StripePaymentForm :publishable-key="stripeConfig.publishableKey" :amount="orderInfo.amount"
                    :order-number="orderInfo.orderNumber" :currency="orderInfo.currency || 'usd'"
                    @success="handlePaymentSuccess" @error="handlePaymentError" />
            </div>

            <!-- PayPal 支付容器 -->
            <div v-if="selectedPaymentMethod === 'paypal'" class="payment-form-container">
                <div id="paypal-button-container"></div>
            </div>

            <!-- 微信支付二维码 -->
            <div v-if="selectedPaymentMethod === 'wechat'" class="payment-form-container">
                <div class="wechat-payment">
                    <div class="qr-code-container">
                        <div id="wechat-qr-code"></div>
                    </div>
                    <p class="wechat-tip">{{ $t('payment.wechat.scanTip', '请使用微信扫描二维码完成支付') }}</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import StripePaymentForm from '@/components/StripePaymentForm.vue'
import { GetCheckoutInfo } from '@/api/checkout'
import { GetOrderPayInfo } from '@/api/order'

const route = useRoute()
const router = useRouter()

// 响应式数据
const selectedPaymentMethod = ref('')
const loading = ref(false)
const checkoutData = ref(null)
const availablePaymentMethods = ref([])

// 订单信息
const orderInfo = reactive({
    orderNumber: route.query.orderNumber || '',
    amount: 0,
    currency: 'usd',
    amountFormat: ''
})

// 支付配置
const stripeConfig = reactive({
    publishableKey: import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || 'pk_test_51234567890abcdef'
})

// 支付方式映射配置
const paymentMethodsConfig = {
    'paypal': {
        id: 'paypal',
        name: 'PayPal',
        icon: '/src/assets/paypal.png',
        component: 'paypal'
    },
    'stripe': {
        id: 'stripe',
        name: 'Stripe',
        icon: '/src/assets/<EMAIL>',
        component: 'stripe'
    },
    'bitpay': {
        id: 'bitpay',
        name: 'BitPay',
        icon: '/src/assets/<EMAIL>',
        component: 'bitpay'
    },
    'wepay': {
        id: 'wepay',
        name: '微信支付',
        icon: '/src/assets/<EMAIL>',
        component: 'wechat'
    },
    'wechat': {
        id: 'wechat',
        name: '微信支付',
        icon: '/src/assets/<EMAIL>',
        component: 'wechat'
    }
}

// 格式化金额显示
const formatAmount = computed(() => {
    return (amount) => {
        if (orderInfo.amountFormat) {
            return orderInfo.amountFormat
        }
        const currency = orderInfo.currency.toUpperCase()
        if (currency === 'USD') {
            return `$${amount.toFixed(2)}`
        } else if (currency === 'CNY') {
            return `¥${amount.toFixed(2)}`
        }
        return `${currency} ${amount.toFixed(2)}`
    }
})

// 获取结算信息
const fetchCheckoutInfo = async () => {
    try {
        loading.value = true
        const response = await GetCheckoutInfo()

        if (response.status === 'success' && response.data) {
            checkoutData.value = response.data

            // 更新订单信息
            if (response.data.amount !== undefined) {
                orderInfo.amount = response.data.amount
            }
            if (response.data.amount_format) {
                orderInfo.amountFormat = response.data.amount_format
            }

            // 处理支付方式
            if (response.data.PaymentMethods && Array.isArray(response.data.PaymentMethods)) {
                availablePaymentMethods.value = response.data.PaymentMethods
                    .filter(method => method && paymentMethodsConfig[method.code])
                    .map(method => ({
                        ...paymentMethodsConfig[method.code],
                        code: method.code,
                        name: method.name || paymentMethodsConfig[method.code].name,
                        icon: method.icon || paymentMethodsConfig[method.code].icon
                    }))

                // 设置默认选中的支付方式
                if (availablePaymentMethods.value.length > 0) {
                    selectedPaymentMethod.value = availablePaymentMethods.value[0].id
                }
            }
        }
    } catch (error) {
        console.error('获取结算信息失败:', error)
        ElMessage.error('获取结算信息失败，请刷新页面重试')
    } finally {
        loading.value = false
    }
}

// 获取订单支付信息（如果有订单号）
const fetchOrderPayInfo = async () => {
    if (!orderInfo.orderNumber) return

    try {
        loading.value = true
        const response = await GetOrderPayInfo(orderInfo.orderNumber)

        if (response.status === 'success' && response.data) {
            // 更新订单信息
            Object.assign(orderInfo, {
                amount: response.data.amount || orderInfo.amount,
                currency: response.data.currency_code || orderInfo.currency,
                amountFormat: response.data.amount_format || ''
            })

            // 处理支付方式配置
            if (response.data.payment_methods) {
                availablePaymentMethods.value = response.data.payment_methods
                    .filter(method => method && paymentMethodsConfig[method.code])
                    .map(method => ({
                        ...paymentMethodsConfig[method.code],
                        code: method.code,
                        name: method.name || paymentMethodsConfig[method.code].name
                    }))

                if (availablePaymentMethods.value.length > 0) {
                    selectedPaymentMethod.value = availablePaymentMethods.value[0].id
                }
            }
        }
    } catch (error) {
        console.error('获取订单支付信息失败:', error)
        ElMessage.error('获取订单支付信息失败')
    } finally {
        loading.value = false
    }
}

// 初始化数据
const initData = async () => {
    // 如果有订单号，优先获取订单支付信息
    if (orderInfo.orderNumber) {
        await fetchOrderPayInfo()
    } else {
        // 否则获取结算信息
        await fetchCheckoutInfo()
    }
}

// 处理支付成功
const handlePaymentSuccess = (result) => {
    console.log('支付成功:', result)
    ElMessage.success('支付成功！')

    // 跳转到支付成功页面
    router.push({
        path: '/payment/success',
        query: {
            orderNumber: result.orderNumber,
            paymentMethod: selectedPaymentMethod.value
        }
    })
}

// 处理支付错误
const handlePaymentError = (error) => {
    console.error('支付失败:', error)
    ElMessage.error(error.message || '支付失败，请重试')
}

// 组件挂载时初始化数据
onMounted(() => {
    initData()
})
</script>

<style lang="scss" scoped>
@import url('./index.scss');

.no-payment-methods {
    text-align: center;
    padding: 40px 20px;
    color: #999;

    p {
        margin: 0;
        font-size: 16px;
    }
}

.payment-methods {
    .el-loading-mask {
        border-radius: 8px;
    }
}
</style>