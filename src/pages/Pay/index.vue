<template>
    <div class="pay_box">
        <div class="pay_box_main">
            <!-- 订单信息 -->
            <div class="order-info">
                <h2 class="order-title">{{ $t('payment.orderSubmitted', '订单提交成功，请付款') }}</h2>
                <div class="order-details">
                    <div class="order-item">
                        <span class="label">{{ $t('payment.orderNumber', '订单号') }}：</span>
                        <span class="value">{{ orderInfo.orderNumber || '123456789' }}</span>
                    </div>
                    <div class="order-item">
                        <span class="label">{{ $t('payment.amount', '支付金额') }}：</span>
                        <span class="value amount">{{ formatAmount(orderInfo.amount || 100) }}</span>
                    </div>
                </div>
            </div>

            <!-- 支付方式选择 -->
            <div class="payment-methods">
                <h3 class="section-title">{{ $t('payment.selectMethod', '选择支付方式') }}</h3>
                <el-radio-group v-model="selectedPaymentMethod" class="payment-options">
                    <el-radio value="stripe" class="payment-option">
                        <div class="payment-option-content">
                            <img src="@assets/<EMAIL>" alt="Stripe" class="payment-logo" />
                            <span>{{ $t('payment.stripe.name', 'Stripe 支付') }}</span>
                        </div>
                    </el-radio>
                    <el-radio value="paypal" class="payment-option">
                        <div class="payment-option-content">
                            <img src="@assets/paypal.png" alt="PayPal" class="payment-logo" />
                            <span>{{ $t('payment.paypal.name', 'PayPal 支付') }}</span>
                        </div>
                    </el-radio>
                    <el-radio value="wechat" class="payment-option">
                        <div class="payment-option-content">
                            <img src="@assets/<EMAIL>" alt="微信支付" class="payment-logo" />
                            <span>{{ $t('payment.wechat.name', '微信支付') }}</span>
                        </div>
                    </el-radio>
                </el-radio-group>
            </div>

            <!-- Stripe 支付表单 -->
            <div v-if="selectedPaymentMethod === 'stripe'" class="payment-form-container">
                <StripePaymentForm :publishable-key="stripeConfig.publishableKey" :amount="orderInfo.amount"
                    :order-number="orderInfo.orderNumber" :currency="orderInfo.currency || 'usd'"
                    @success="handlePaymentSuccess" @error="handlePaymentError" />
            </div>

            <!-- PayPal 支付容器 -->
            <div v-if="selectedPaymentMethod === 'paypal'" class="payment-form-container">
                <div id="paypal-button-container"></div>
            </div>

            <!-- 微信支付二维码 -->
            <div v-if="selectedPaymentMethod === 'wechat'" class="payment-form-container">
                <div class="wechat-payment">
                    <div class="qr-code-container">
                        <div id="wechat-qr-code"></div>
                    </div>
                    <p class="wechat-tip">{{ $t('payment.wechat.scanTip', '请使用微信扫描二维码完成支付') }}</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import StripePaymentForm from '@/components/StripePaymentForm.vue'

const route = useRoute()
const router = useRouter()

// 响应式数据
const selectedPaymentMethod = ref('stripe')

// 订单信息
const orderInfo = reactive({
    orderNumber: route.query.orderNumber || '123456789',
    amount: parseFloat(route.query.amount) || 100.00,
    currency: route.query.currency || 'usd'
})

// 支付配置
const stripeConfig = reactive({
    publishableKey: import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || 'pk_test_51234567890abcdef'
})

// 格式化金额显示
const formatAmount = computed(() => {
    return (amount) => {
        const currency = orderInfo.currency.toUpperCase()
        if (currency === 'USD') {
            return `$${amount.toFixed(2)}`
        } else if (currency === 'CNY') {
            return `¥${amount.toFixed(2)}`
        }
        return `${currency} ${amount.toFixed(2)}`
    }
})

// 处理支付成功
const handlePaymentSuccess = (result) => {
    console.log('支付成功:', result)
    ElMessage.success('支付成功！')

    // 跳转到支付成功页面
    router.push({
        path: '/payment/success',
        query: {
            orderNumber: result.orderNumber,
            paymentMethod: selectedPaymentMethod.value
        }
    })
}

// 处理支付错误
const handlePaymentError = (error) => {
    console.error('支付失败:', error)
    ElMessage.error(error.message || '支付失败，请重试')
}
</script>

<style lang="scss" scoped>
@import url('./index.scss');
</style>