# 支付页面接口对接说明

## 修改概述

已成功对接 `GetCheckoutInfo` 接口，使支付页面能够展示真实的结算数据，并根据后端返回的支付方式动态显示对应的支付选项。

## 主要修改内容

### 1. 接口对接

- **GetCheckoutInfo**: 获取结算页信息，包含支付方式列表
- **GetOrderPayInfo**: 获取订单支付信息（当有订单号时使用）

### 2. 数据结构优化

#### 新增响应式数据：
```javascript
const checkoutData = ref(null)              // 结算数据
const availablePaymentMethods = ref([])     // 可用支付方式
const loading = ref(false)                  // 加载状态
```

#### 支付方式映射配置：
```javascript
const paymentMethodsConfig = {
    'paypal': { id: 'paypal', name: 'PayPal', icon: '/src/assets/paypal.png' },
    'stripe': { id: 'stripe', name: 'Stripe', icon: '/src/assets/<EMAIL>' },
    'bitpay': { id: 'bitpay', name: 'BitPay', icon: '/src/assets/<EMAIL>' },
    'wepay': { id: 'wepay', name: '微信支付', icon: '/src/assets/<EMAIL>' },
    'wechat': { id: 'wechat', name: '微信支付', icon: '/src/assets/<EMAIL>' }
}
```

### 3. 核心功能实现

#### 数据获取逻辑：
1. **有订单号**: 调用 `GetOrderPayInfo` 获取订单支付信息
2. **无订单号**: 调用 `GetCheckoutInfo` 获取结算信息

#### 支付方式处理：
- 根据接口返回的 `PaymentMethods` 数组过滤可用支付方式
- 使用配置映射将后端支付方式代码转换为前端组件
- 自动选择第一个可用的支付方式作为默认选项

#### 金额显示优化：
- 优先使用后端返回的格式化金额 `amount_format`
- 支持多币种显示（USD、CNY等）

### 4. 用户界面改进

#### 动态支付方式显示：
```vue
<el-radio-group v-model="selectedPaymentMethod" class="payment-options">
    <el-radio 
        v-for="method in availablePaymentMethods" 
        :key="method.id"
        :value="method.id" 
        class="payment-option"
    >
        <div class="payment-option-content">
            <img :src="method.icon" :alt="method.name" class="payment-logo" />
            <span>{{ method.name }}</span>
        </div>
    </el-radio>
</el-radio-group>
```

#### 加载状态和空状态处理：
- 添加加载动画 `v-loading="loading"`
- 无支付方式时显示提示信息

## 接口数据结构

### GetCheckoutInfo 返回数据：
```json
{
    "status": "success",
    "data": {
        "amount": 100.00,
        "amount_format": "$100.00",
        "PaymentMethods": [
            {
                "code": "paypal",
                "name": "PayPal",
                "icon": "/path/to/icon.png"
            }
        ]
    }
}
```

### GetOrderPayInfo 返回数据：
```json
{
    "status": "success", 
    "data": {
        "amount": 100.00,
        "currency_code": "USD",
        "amount_format": "$100.00",
        "payment_methods": [
            {
                "code": "stripe",
                "name": "Stripe Payment"
            }
        ]
    }
}
```

## 使用方式

### 1. 从结算页跳转（无订单号）
```javascript
router.push('/pay')
// 自动调用 GetCheckoutInfo 获取结算信息
```

### 2. 从订单页跳转（有订单号）
```javascript
router.push({
    path: '/pay',
    query: { orderNumber: 'ORDER123456' }
})
// 自动调用 GetOrderPayInfo 获取订单支付信息
```

## 错误处理

- 接口调用失败时显示错误提示
- 无可用支付方式时显示友好提示
- 支持重试机制

## 兼容性说明

- 保持与现有支付组件的兼容性
- 支持 Stripe、PayPal、BitPay、微信支付等多种支付方式
- 向后兼容原有的 URL 参数传递方式
